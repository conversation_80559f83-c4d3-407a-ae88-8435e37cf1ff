import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// E-BaaS Admin Configuration
interface EBaaSConfig {
  url: string;
  apiKey: string;
}

// User information from JWT token
interface EBaaSUser {
  id: string;
  email: string;
  role: string;
  adminRole?: 'platform_admin' | 'workspace_admin' | 'workspace_owner';
  workspaceId: string;
  permissions: string[];
  isPlatformAdmin: boolean;
  isWorkspaceAdmin: boolean;
  canAccessAdmin: boolean;
}

// Request options for makeAuthenticatedRequest
interface RequestOptions {
  method?: string;
  body?: any;
  isFormData?: boolean;
}

// Context type definition
interface EBaaSContextType {
  config: EBaaSConfig | null;
  user: EBaaSUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  setConfig: (config: EBaaSConfig) => void;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  getHeaders: () => Record<string, string>;
  apiUrl: (endpoint: string) => string;
  makeAuthenticatedRequest: (endpoint: string, options?: RequestOptions) => Promise<any>;
}

const EBaaSContext = createContext<EBaaSContextType | undefined>(undefined);

export function EBaaSProvider({ children }: { children: ReactNode }) {
  const [config, setConfigState] = useState<EBaaSConfig | null>(null);
  const [user, setUser] = useState<EBaaSUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load config from localStorage on mount
    const savedConfig = localStorage.getItem('ebaas-config');
    const savedToken = localStorage.getItem('ebaas-token');
    
    if (savedConfig) {
      const parsedConfig = JSON.parse(savedConfig);
      setConfigState(parsedConfig);
    } else {
      // Default E-BaaS configuration
      const defaultConfig = {
        url: 'http://localhost:4000',
        apiKey: 'demo-api-key' // This should be configured by admin
      };
      setConfigState(defaultConfig);
      localStorage.setItem('ebaas-config', JSON.stringify(defaultConfig));
    }

    if (savedToken) {
      // Verify token and load user info
      verifyToken(savedToken);
    } else {
      setIsLoading(false);
    }
  }, []);

  const setConfig = (newConfig: EBaaSConfig) => {
    setConfigState(newConfig);
    localStorage.setItem('ebaas-config', JSON.stringify(newConfig));
  };

  const verifyToken = async (token: string) => {
    try {
      if (!config) return;
      
      const response = await fetch(`${config.url}/auth/v1/user`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      } else {
        // Token invalid, remove it
        localStorage.removeItem('ebaas-token');
        setUser(null);
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      localStorage.removeItem('ebaas-token');
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      if (!config) return false;
      
      const response = await fetch(`${config.url}/auth/v1/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': config.apiKey
        },
        body: JSON.stringify({ email, password })
      });

      if (response.ok) {
        const data = await response.json();
        const token = data.access_token;
        
        // Store token
        localStorage.setItem('ebaas-token', token);
        
        // Set user data directly from login response
        if (data.user) {
          setUser(data.user);
          
          // Check if user has admin permissions
          if (data.user.canAccessAdmin) {
            return true;
          } else {
            // User doesn't have admin access, logout
            logout();
            return false;
          }
        } else {
          // Fallback: verify token if user data not in response
          await verifyToken(token);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem('ebaas-token');
    setUser(null);
  };

  const getHeaders = (): Record<string, string> => {
    const token = localStorage.getItem('ebaas-token');
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (config?.apiKey) {
      headers['apikey'] = config.apiKey;
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  };

  const apiUrl = (endpoint: string): string => {
    if (!config) return '';
    const baseUrl = config.url.endsWith('/') ? config.url.slice(0, -1) : config.url;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${baseUrl}${cleanEndpoint}`;
  };

  const makeAuthenticatedRequest = async (endpoint: string, options: RequestOptions = {}): Promise<any> => {
    if (!config) throw new Error('E-BaaS not configured');
    
    const { method = 'GET', body, isFormData = false } = options;
    const url = apiUrl(endpoint);
    
    const headers = getHeaders();
    
    // Handle FormData requests
    if (isFormData) {
      delete headers['Content-Type']; // Let browser set content-type for FormData
    }
    
    const fetchOptions: RequestInit = {
      method,
      headers
    };
    
    if (body) {
      if (isFormData) {
        fetchOptions.body = body; // body is already FormData
      } else {
        fetchOptions.body = JSON.stringify(body);
      }
    }
    
    const response = await fetch(url, fetchOptions);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Request failed: ${response.status} ${errorText}`);
    }
    
    // Handle empty responses
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    }
    
    return await response.text();
  };

  const isAuthenticated = !!user;

  return (
    <EBaaSContext.Provider value={{
      config,
      user,
      isAuthenticated,
      isLoading,
      setConfig,
      login,
      logout,
      getHeaders,
      apiUrl,
      makeAuthenticatedRequest
    }}>
      {children}
    </EBaaSContext.Provider>
  );
}

export function useEBaaS() {
  const context = useContext(EBaaSContext);
  if (context === undefined) {
    throw new Error('useEBaaS must be used within a EBaaSProvider');
  }
  return context;
}