import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Key, 
  Database as DatabaseIcon,
  Edit,
  Trash2,
  Filter,
  Folder,
  File,
  Upload,
  Download,
  Copy,
  CheckCircle,
  XCircle
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEBaaS } from "@/contexts/EBaaSContext";
import { useToast } from "@/hooks/use-toast";

interface StorageBucket {
  id: string;
  name: string;
  public: boolean;
  created_at: string;
  updated_at: string;
  file_size_limit?: number;
  allowed_mime_types?: string[];
}

interface StorageFile {
  name: string;
  id?: string;
  updated_at?: string;
  created_at?: string;
  last_accessed_at?: string;
  metadata?: {
    size?: number;
    mimetype?: string;
    lastModified?: string;
  };
}

export default function Storage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBucket, setSelectedBucket] = useState<string>("");
  const [buckets, setBuckets] = useState<StorageBucket[]>([]);
  const [files, setFiles] = useState<StorageFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [newBucketName, setNewBucketName] = useState("");
  const [isPublic, setIsPublic] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { config, isAuthenticated, makeAuthenticatedRequest } = useEBaaS();
  const { toast } = useToast();

  const fetchBuckets = async () => {
    if (!isAuthenticated) return;
    
    setLoading(true);
    try {
      const data = await makeAuthenticatedRequest('/admin/v1/storage/buckets');
      setBuckets(data || []);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: "Erro ao carregar buckets: " + error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchFiles = async (bucketName: string) => {
    if (!isAuthenticated) return;
    
    setLoadingFiles(true);
    try {
      const data = await makeAuthenticatedRequest(`/admin/v1/storage/files/${bucketName}`);
      setFiles(data || []);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: "Erro ao carregar arquivos: " + error.message,
        variant: "destructive",
      });
    } finally {
      setLoadingFiles(false);
    }
  };

  const createBucket = async () => {
    if (!isAuthenticated || !newBucketName.trim()) return;

    try {
      await makeAuthenticatedRequest('/admin/v1/storage/buckets', {
        method: 'POST',
        body: {
          name: newBucketName,
          public: isPublic
        }
      });

      setNewBucketName("");
      setIsPublic(false);
      await fetchBuckets();
      
      toast({
        title: "Sucesso",
        description: "Bucket criado com sucesso!",
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const uploadFile = async (file: File) => {
    if (!isAuthenticated || !selectedBucket) return;

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      await makeAuthenticatedRequest(`/admin/v1/storage/upload/${selectedBucket}`, {
        method: 'POST',
        body: formData,
        isFormData: true
      });

      await fetchFiles(selectedBucket);
      
      toast({
        title: "Sucesso",
        description: "Arquivo enviado com sucesso!",
      });
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchBuckets();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (selectedBucket) {
      fetchFiles(selectedBucket);
    }
  }, [selectedBucket]);

  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Storage</h1>
          <p className="text-muted-foreground mt-2">
            Gerencie seus arquivos e buckets
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Novo Bucket
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="p-2 space-y-1">
                <Label htmlFor="bucket-name">Nome do Bucket</Label>
                <Input
                  id="bucket-name"
                  placeholder="Nome do bucket"
                  value={newBucketName}
                  onChange={(e) => setNewBucketName(e.target.value)}
                />
              </div>
              <div className="p-2 space-y-1">
                <Label htmlFor="bucket-public" className="flex items-center space-x-2">
                  <Input
                    type="checkbox"
                    id="bucket-public"
                    checked={isPublic}
                    onChange={(e) => setIsPublic(e.target.checked)}
                  />
                  <span>Tornar público?</span>
                </Label>
              </div>
              <DropdownMenuItem onClick={createBucket} disabled={!newBucketName.trim()}>
                Criar Bucket
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button className="bg-primary hover:bg-primary/90" onClick={() => fileInputRef.current?.click()}>
            <Upload className="w-4 h-4 mr-2" />
            Upload Arquivo
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: 'none' }}
              onChange={(e) => {
                if (e.target.files && e.target.files.length > 0) {
                  uploadFile(e.target.files[0]);
                }
              }}
            />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3 space-y-6">
          <Card className="gradient-border">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Arquivos</CardTitle>
                  <CardDescription>
                    {loadingFiles ? "Carregando..." : `${filteredFiles.length} arquivos`}
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Buscar arquivos..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-9 w-64"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loadingFiles ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Carregando arquivos...</p>
                </div>
              ) : !selectedBucket ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Selecione um bucket para ver os arquivos</p>
                </div>
              ) : filteredFiles.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Nenhum arquivo encontrado</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Tamanho</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredFiles.map((file) => (
                      <TableRow key={file.name} className="hover:bg-accent/50 transition-colors">
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <File className="w-4 h-4 text-muted-foreground" />
                            <span>{file.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{formatFileSize(file.metadata?.size)}</TableCell>
                        <TableCell className="text-muted-foreground">{file.metadata?.mimetype}</TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="bg-popover border border-border">
                              <DropdownMenuItem>
                                <Download className="w-4 h-4 mr-2" />
                                Download
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Copy className="w-4 h-4 mr-2" />
                                Gerar Link Público
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-destructive">
                                <Trash2 className="w-4 h-4 mr-2" />
                                Deletar Arquivo
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card className="gradient-border">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Folder className="w-5 h-5" />
                <span>Buckets</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {loading ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">Carregando buckets...</p>
                </div>
              ) : buckets.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">Nenhum bucket encontrado</p>
                </div>
              ) : (
                buckets.map((bucket) => (
                  <Button
                    key={bucket.id}
                    variant={selectedBucket === bucket.id ? "default" : "outline"}
                    className="w-full text-left h-auto p-3 whitespace-normal text-sm"
                    onClick={() => setSelectedBucket(bucket.id)}
                  >
                    <div className="flex items-center justify-between w-full">
                      <span>{bucket.name}</span>
                      {bucket.public ? (
                        <Badge variant="secondary">Público</Badge>
                      ) : (
                        <Badge variant="outline">Privado</Badge>
                      )}
                    </div>
                  </Button>
                ))
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
