import { ReactNode } from 'react';
import { useEBaaS } from '@/contexts/EBaaSContext';
import { Navigate } from 'react-router-dom';

interface AuthWrapperProps {
  children: ReactNode;
  requireAdmin?: boolean;
  requirePlatformAdmin?: boolean;
}

export function AuthWrapper({ 
  children, 
  requireAdmin = true, 
  requirePlatformAdmin = false 
}: AuthWrapperProps) {
  const { 
    isAuthenticated, 
    isLoading, 
    user
  } = useEBaaS();
  
  const canAccessAdmin = user?.canAccessAdmin;
  const isPlatformAdmin = user?.isPlatformAdmin;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requireAdmin && !canAccessAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Access Denied</h2>
          <p className="text-muted-foreground">You don't have admin permissions.</p>
        </div>
      </div>
    );
  }

  if (requirePlatformAdmin && !isPlatformAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Platform Admin Required</h2>
          <p className="text-muted-foreground">This feature requires platform admin access.</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}