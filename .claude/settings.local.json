{"permissions": {"allow": ["Bash(find:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx typeorm migration:create:*)", "Bash(yarn add:*)", "<PERSON><PERSON>(touch:*)", "Bash(grep:*)", "Bash(npm run migrations:create:*)", "Bash(npm run migrations:run:*)", "Bash(npm run build:*)", "Bash(ls:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm run typecheck:*)", "Bash(rg:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run:*)", "Bash(NODE_ENV=development ts-node --transpile-only src/index.ts)", "<PERSON><PERSON>(docker-compose ps:*)", "<PERSON><PERSON>(docker-compose down:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(curl:*)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose exec:*)", "Bash(timeout 5s NODE_ENV=development npm start 2 >& 1)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/tmp/fix_entities.sh:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(timeout 10s npm run dev)", "<PERSON><PERSON>(sed:*)", "Bash(kill:*)", "<PERSON><PERSON>(cat:*)", "Bash(npx tsc:*)", "Bash(npx ts-node:*)", "<PERSON><PERSON>(./admin.sh)", "Bash(docker volume prune:*)", "Bash(npm start)"], "deny": []}}