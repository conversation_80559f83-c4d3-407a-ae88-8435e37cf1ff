
> e-baas@1.0.0 dev:postgres
> ts-node --transpile-only src/postgres-server.ts

✅ Database connection initialized successfully!
✅ Default workspace created: baff8d18-d43b-4e59-98b9-b01f494e0177
✅ Default admin user created: contato.luc<PERSON><PERSON><PERSON><PERSON>@gmail.com (ID: 4e53028a-e7db-464a-94a1-32d4ef18f400)
✅ User assigned to workspace: baff8d18-d43b-4e59-98b9-b01f494e0177
🚀 E-BaaS PostgreSQL Backend Server running on port 4000
📊 Health check: http://localhost:4000/
🔐 Auth endpoint: http://localhost:4000/auth/v1
💾 Database endpoint: http://localhost:4000/rest/v1
🐘 PostgreSQL Database: localhost:5433/ebaas_main
✨ PostgreSQL backend server is ready!
