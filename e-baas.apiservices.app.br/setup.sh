#!/bin/bash

# E-BaaS Development Environment Setup Script

echo "🚀 E-BaaS Development Environment Setup"
echo "======================================="

# Load environment variables
if [ -f .env ]; then
    source <(grep -v '^#' .env | grep -v '^$' | sed 's/^/export /')
fi

ADMIN_EMAIL=${DEFAULT_ADMIN_EMAIL:-"<EMAIL>"}
ADMIN_PASSWORD=${DEFAULT_ADMIN_PASSWORD:-"E-b@@s*2025"}
BACKEND_URL="http://localhost:${PORT:-4000}"

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker Desktop and try again."
        exit 1
    fi
    echo "✅ Docker is running"
}

# Function to start database services
start_services() {
    echo ""
    echo "🐘 Starting PostgreSQL and Redis services..."
    
    # Stop existing containers if they exist
    docker-compose -f docker-compose.dev.yml down 2>/dev/null
    
    # Start services
    docker-compose -f docker-compose.dev.yml up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ Services started successfully"
        
        # Wait for database to be ready
        echo "⏳ Waiting for PostgreSQL to be ready..."
        timeout=60
        while [ $timeout -gt 0 ]; do
            if docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U postgres -d ebaas_main > /dev/null 2>&1; then
                echo "✅ PostgreSQL is ready"
                break
            fi
            sleep 2
            timeout=$((timeout - 2))
        done
        
        if [ $timeout -le 0 ]; then
            echo "❌ PostgreSQL failed to start within 60 seconds"
            return 1
        fi
        
        return 0
    else
        echo "❌ Failed to start services"
        return 1
    fi
}

# Function to stop services
stop_services() {
    echo ""
    echo "🛑 Stopping services..."
    docker-compose -f docker-compose.dev.yml down
    echo "✅ Services stopped"
}

# Function to restart services
restart_services() {
    stop_services
    start_services
}

# Function to show service status
status_services() {
    echo ""
    echo "📊 Service Status:"
    echo "=================="
    docker-compose -f docker-compose.dev.yml ps
    
    echo ""
    echo "🔗 Service URLs:"
    echo "PostgreSQL: localhost:5433"
    echo "Redis: localhost:6380"
    echo "Backend API: $BACKEND_URL"
    echo "Frontend: http://localhost:8080"
}

# Function to show logs
show_logs() {
    echo ""
    echo "📋 Service Logs:"
    echo "================"
    docker-compose -f docker-compose.dev.yml logs -f
}

# Function to reset database
reset_database() {
    echo ""
    echo "🗄️  Resetting database..."
    echo "⚠️  This will delete all data. Are you sure? (y/N)"
    read -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose -f docker-compose.dev.yml down -v
        docker volume rm ebaas-postgres-dev_postgres_data 2>/dev/null || true
        docker volume rm ebaas-postgres-dev_redis_data 2>/dev/null || true
        echo "✅ Database reset complete"
        start_services
    else
        echo "Database reset cancelled"
    fi
}

# Function to create admin user
create_admin() {
    echo ""
    echo "👤 Creating admin user in database..."
    
    # Check if backend is running
    if ! curl -s "$BACKEND_URL/" > /dev/null; then
        echo "❌ Backend is not running. Please start it first with: npm run dev:postgres"
        return 1
    fi
    
    # Try to create admin user via API
    SIGNUP_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "apikey: ebaas_dev-key" \
        -d "{
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$ADMIN_PASSWORD\",
            \"role\": \"platform_admin\",
            \"adminRole\": \"platform_admin\",
            \"isPlatformAdmin\": true,
            \"canAccessAdmin\": true
        }" \
        "$BACKEND_URL/auth/v1/signup")
    
    HTTP_STATUS=$(echo "$SIGNUP_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$SIGNUP_RESPONSE" | sed '/HTTP_STATUS:/d')
    
    if [ "$HTTP_STATUS" = "201" ] || [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ Admin user created successfully!"
        echo "📧 Email: $ADMIN_EMAIL"
        echo "🔐 Password: $ADMIN_PASSWORD"
    elif [ "$HTTP_STATUS" = "400" ] && echo "$RESPONSE_BODY" | grep -q "already exists"; then
        echo "✅ Admin user already exists"
        echo "📧 Email: $ADMIN_EMAIL"
    else
        echo "❌ Failed to create admin user"
        echo "HTTP Status: $HTTP_STATUS"
        echo "Response: $RESPONSE_BODY"
        return 1
    fi
}

# Function to test login
test_login() {
    echo ""
    echo "🧪 Testing admin login..."
    
    LOGIN_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "apikey: ebaas_dev-key" \
        -d "{
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$ADMIN_PASSWORD\"
        }" \
        "$BACKEND_URL/auth/v1/login")
    
    HTTP_STATUS=$(echo "$LOGIN_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
    
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ Login test successful!"
        return 0
    else
        echo "❌ Login test failed"
        return 1
    fi
}

# Function to show help
show_help() {
    echo ""
    echo "📖 Available Commands:"
    echo "====================="
    echo "./setup.sh start     - Start PostgreSQL and Redis services"
    echo "./setup.sh stop      - Stop all services"
    echo "./setup.sh restart   - Restart all services"
    echo "./setup.sh status    - Show service status"
    echo "./setup.sh logs      - Show service logs"
    echo "./setup.sh reset     - Reset database (WARNING: deletes all data)"
    echo "./setup.sh admin     - Create admin user"
    echo "./setup.sh test      - Test admin login"
    echo "./setup.sh full      - Full setup (start services + create admin)"
    echo "./setup.sh help      - Show this help"
    echo ""
    echo "🔧 Development Workflow:"
    echo "1. ./setup.sh full              # Setup everything"
    echo "2. npm run dev:postgres          # Start backend"
    echo "3. cd ../admin.e-baas.apiservices.app.br && npm run dev  # Start frontend"
}

# Main command handling
case "${1:-}" in
    "start")
        check_docker
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        check_docker
        restart_services
        ;;
    "status")
        status_services
        ;;
    "logs")
        show_logs
        ;;
    "reset")
        check_docker
        reset_database
        ;;
    "admin")
        create_admin
        ;;
    "test")
        test_login
        ;;
    "full")
        check_docker
        start_services
        sleep 5
        create_admin
        test_login
        echo ""
        echo "🎉 Full setup completed!"
        echo ""
        echo "📝 Next steps:"
        echo "1. Start backend: npm run dev:postgres"
        echo "2. Start frontend: cd ../admin.e-baas.apiservices.app.br && npm run dev"
        echo "3. Open frontend: http://localhost:8080"
        echo "4. Login with: $ADMIN_EMAIL / $ADMIN_PASSWORD"
        ;;
    "help"|"")
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        show_help
        exit 1
        ;;
esac