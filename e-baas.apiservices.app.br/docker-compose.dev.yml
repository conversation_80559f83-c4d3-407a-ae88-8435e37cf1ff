version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: ebaas-postgres-dev
    environment:
      POSTGRES_DB: ebaas_main
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - ebaas-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ebaas_main"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: ebaas-redis-dev
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - ebaas-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

volumes:
  postgres_data:
  redis_data:

networks:
  ebaas-network:
    driver: bridge