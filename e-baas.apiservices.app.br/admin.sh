#!/bin/bash

# E-BaaS Admin User Management Script
# This script creates and manages the super admin user

# Load environment variables from .env file
if [ -f .env ]; then
    source <(grep -v '^#' .env | grep -v '^$' | sed 's/^/export /')
fi

# Default values if not set in .env
BACKEND_URL=${PORT:+"http://localhost:$PORT"}
BACKEND_URL=${BACKEND_URL:-"http://localhost:4000"}
ADMIN_EMAIL=${DEFAULT_ADMIN_EMAIL:-"<EMAIL>"}
ADMIN_PASSWORD=${DEFAULT_ADMIN_PASSWORD:-"E-b@@s*2025"}
ADMIN_ROLE=${DEFAULT_ADMIN_ROLE:-"platform_admin"}
API_KEY=${API_KEY_PREFIX:-"ebaas_"}dev-key

echo "👤 E-BaaS Admin User Management"
echo "==============================="
echo "Backend URL: $BACKEND_URL"
echo "Admin Email: $ADMIN_EMAIL"
echo "Admin Role: $ADMIN_ROLE"
echo ""
echo "ℹ️  Note: The admin user is automatically created when the backend starts."
echo "This script verifies the user exists and tests login functionality."
echo ""

# Function to check if backend is running
check_backend() {
    echo "🔍 Checking if backend is running..."
    if curl -s "$BACKEND_URL/" > /dev/null 2>&1; then
        echo "✅ Backend is running at $BACKEND_URL"
        return 0
    else
        echo "❌ Backend is not running at $BACKEND_URL"
        echo ""
        echo "📝 To start the backend with PostgreSQL:"
        echo "1. Start services: ./setup.sh start"
        echo "2. Start backend: npm run dev:postgres"
        return 1
    fi
}

# Function to create super admin user
create_super_admin() {
    echo ""
    echo "👤 Creating super admin user..."
    
    # Signup request
    SIGNUP_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "apikey: $API_KEY" \
        -d "{
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$ADMIN_PASSWORD\",
            \"role\": \"$ADMIN_ROLE\",
            \"adminRole\": \"platform_admin\",
            \"isPlatformAdmin\": true,
            \"canAccessAdmin\": true
        }" \
        "$BACKEND_URL/auth/v1/signup")
    
    HTTP_STATUS=$(echo "$SIGNUP_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$SIGNUP_RESPONSE" | sed '/HTTP_STATUS:/d')
    
    if [ "$HTTP_STATUS" = "201" ] || [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ Super admin user created successfully!"
        echo "📧 Email: $ADMIN_EMAIL"
        echo "🔐 Password: $ADMIN_PASSWORD"
        echo "👑 Role: $ADMIN_ROLE"
        echo ""
        echo "Response: $RESPONSE_BODY"
    elif [ "$HTTP_STATUS" = "400" ] && echo "$RESPONSE_BODY" | grep -q "already exists"; then
        echo "⚠️  User already exists, trying to update permissions..."
        update_admin_permissions
    else
        echo "❌ Failed to create super admin user"
        echo "HTTP Status: $HTTP_STATUS"
        echo "Response: $RESPONSE_BODY"
        return 1
    fi
}

# Function to update admin permissions for existing user
update_admin_permissions() {
    echo "🔄 Updating admin permissions..."
    
    # Login to get token
    LOGIN_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "apikey: $API_KEY" \
        -d "{
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$ADMIN_PASSWORD\"
        }" \
        "$BACKEND_URL/auth/v1/login")
    
    HTTP_STATUS=$(echo "$LOGIN_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$LOGIN_RESPONSE" | sed '/HTTP_STATUS:/d')
    
    if [ "$HTTP_STATUS" = "200" ]; then
        TOKEN=$(echo "$RESPONSE_BODY" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
        if [ -n "$TOKEN" ]; then
            echo "✅ Login successful, token obtained"
            echo "🔐 User already exists with admin privileges"
        else
            echo "⚠️  Login successful but no token found"
        fi
    else
        echo "⚠️  Could not login with existing user"
        echo "Response: $RESPONSE_BODY"
    fi
}

# Function to test login
test_login() {
    echo ""
    echo "🧪 Testing admin login..."
    
    LOGIN_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "apikey: $API_KEY" \
        -d "{
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$ADMIN_PASSWORD\"
        }" \
        "$BACKEND_URL/auth/v1/login")
    
    HTTP_STATUS=$(echo "$LOGIN_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
    RESPONSE_BODY=$(echo "$LOGIN_RESPONSE" | sed '/HTTP_STATUS:/d')
    
    if [ "$HTTP_STATUS" = "200" ]; then
        echo "✅ Login test successful!"
        TOKEN=$(echo "$RESPONSE_BODY" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
        if [ -n "$TOKEN" ]; then
            echo "🎫 Token obtained: ${TOKEN:0:20}..."
            
            # Test authenticated endpoint
            echo ""
            echo "🔍 Testing authenticated endpoint..."
            AUTH_TEST=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
                -H "Authorization: Bearer $TOKEN" \
                -H "apikey: $API_KEY" \
                "$BACKEND_URL/auth/v1/user")
            
            AUTH_HTTP_STATUS=$(echo "$AUTH_TEST" | grep "HTTP_STATUS:" | cut -d: -f2)
            if [ "$AUTH_HTTP_STATUS" = "200" ]; then
                echo "✅ Authentication test successful!"
            else
                echo "⚠️  Authentication test failed"
            fi
        fi
    else
        echo "❌ Login test failed"
        echo "HTTP Status: $HTTP_STATUS"
        echo "Response: $RESPONSE_BODY"
    fi
}

# Main execution
main() {
    if ! check_backend; then
        exit 1
    fi
    
    create_super_admin
    test_login
    
    echo ""
    echo "🎉 Admin setup completed!"
    echo ""
    echo "📝 Next steps:"
    echo "1. Open the frontend: http://localhost:8080"
    echo "2. Login with email: $ADMIN_EMAIL"
    echo "3. Use password: $ADMIN_PASSWORD"
    echo ""
    echo "🔧 To recreate the user, run this script again:"
    echo "./admin.sh"
}

# Run main function
main