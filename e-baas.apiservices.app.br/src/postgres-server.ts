import cors from "cors";
import { AppDataSource } from "./infra/database/data-source";
import dotenv from "dotenv";
import express, { Express, Request, Response } from "express";
import { createServer } from "http";
import bcrypt from "bcrypt";
import adminRouter from "./modules/admin/admin.controller";

dotenv.config();

const app: Express = express();
app.use(express.json());
app.use(
  cors({
    origin: "*",
  })
);

// Health check endpoint
app.get("/", (req: Request, res: Response) => {
  res.json({ 
    message: "E-BaaS Backend Server Running", 
    timestamp: new Date().toISOString(),
    status: "OK",
    version: "1.0.0",
    database: AppDataSource.isInitialized ? "connected" : "disconnected",
    database_type: "postgresql"
  });
});

// Auth endpoints
app.post("/auth/v1/signup", async (req: Request, res: Response) => {
  try {
    if (!AppDataSource.isInitialized) {
      return res.status(500).json({ error: "Database not connected" });
    }

    const { email, password, role = "user", adminRole, isPlatformAdmin, canAccessAdmin } = req.body;
    
    // Check if user already exists
    const existingUser = await AppDataSource.query(
      "SELECT id FROM users WHERE email = $1", 
      [email]
    );
    
    if (existingUser.length > 0) {
      return res.status(400).json({ error: "User already exists" });
    }
    
    // Hash password
    const passwordHash = await bcrypt.hash(password, 10);
    
    // Create new user
    const result = await AppDataSource.query(`
      INSERT INTO users (
        email, password_hash, role, admin_role, 
        is_platform_admin, is_workspace_admin, can_access_admin,
        workspace_id, permissions
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING id, email, role, admin_role, is_platform_admin, is_workspace_admin, can_access_admin
    `, [
      email,
      passwordHash,
      role,
      adminRole || (isPlatformAdmin ? 'platform_admin' : 'user'),
      !!isPlatformAdmin,
      !!canAccessAdmin,
      !!canAccessAdmin || !!isPlatformAdmin,
      'default_workspace',
      isPlatformAdmin ? ['*'] : ['read']
    ]);
    
    const newUser = result[0];
    
    // Generate mock JWT token
    const token = `jwt_${Buffer.from(JSON.stringify({ 
      userId: newUser.id, 
      email: newUser.email,
      role: newUser.role,
      adminRole: newUser.admin_role,
      canAccessAdmin: newUser.can_access_admin
    })).toString('base64')}`;
    
    res.status(201).json({
      access_token: token,
      token_type: "bearer",
      expires_in: 3600,
      user: {
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
        adminRole: newUser.admin_role,
        canAccessAdmin: newUser.can_access_admin
      }
    });
  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.post("/auth/v1/login", async (req: Request, res: Response) => {
  try {
    if (!AppDataSource.isInitialized) {
      return res.status(500).json({ error: "Database not connected" });
    }

    const { email, password } = req.body;
    
    // Find user
    const users = await AppDataSource.query(`
      SELECT id, email, password_hash, role, admin_role, workspace_id, 
             permissions, is_platform_admin, is_workspace_admin, can_access_admin
      FROM users WHERE email = $1
    `, [email]);
    
    if (users.length === 0) {
      return res.status(401).json({ error: "Invalid credentials" });
    }
    
    const user = users[0];
    
    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({ error: "Invalid credentials" });
    }
    
    // Update last sign in
    await AppDataSource.query(
      "UPDATE users SET last_sign_in_at = NOW(), sign_in_count = sign_in_count + 1 WHERE id = $1",
      [user.id]
    );
    
    // Generate JWT token
    const token = `jwt_${Buffer.from(JSON.stringify({ 
      userId: user.id, 
      email: user.email,
      role: user.role,
      adminRole: user.admin_role,
      canAccessAdmin: user.can_access_admin
    })).toString('base64')}`;
    
    res.json({
      access_token: token,
      token_type: "bearer",
      expires_in: 3600,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        adminRole: user.admin_role,
        workspaceId: user.workspace_id,
        permissions: user.permissions,
        isPlatformAdmin: user.is_platform_admin,
        isWorkspaceAdmin: user.is_workspace_admin,
        canAccessAdmin: user.can_access_admin
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.get("/auth/v1/user", async (req: Request, res: Response) => {
  try {
    if (!AppDataSource.isInitialized) {
      return res.status(500).json({ error: "Database not connected" });
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: "No token provided" });
    }
    
    const token = authHeader.split(' ')[1];
    if (!token.startsWith('jwt_')) {
      return res.status(401).json({ error: "Invalid token" });
    }
    
    // Decode token
    const payload = JSON.parse(Buffer.from(token.replace('jwt_', ''), 'base64').toString());
    
    const users = await AppDataSource.query(`
      SELECT id, email, role, admin_role, workspace_id, permissions,
             is_platform_admin, is_workspace_admin, can_access_admin
      FROM users WHERE id = $1
    `, [payload.userId]);
    
    if (users.length === 0) {
      return res.status(401).json({ error: "User not found" });
    }
    
    const user = users[0];
    
    res.json({
      id: user.id,
      email: user.email,
      role: user.role,
      adminRole: user.admin_role,
      workspaceId: user.workspace_id,
      permissions: user.permissions,
      isPlatformAdmin: user.is_platform_admin,
      isWorkspaceAdmin: user.is_workspace_admin,
      canAccessAdmin: user.can_access_admin
    });
  } catch (error) {
    console.error('User fetch error:', error);
    res.status(401).json({ error: "Invalid token" });
  }
});

// Health endpoints
app.get("/auth/v1/health", async (req: Request, res: Response) => {
  try {
    if (!AppDataSource.isInitialized) {
      return res.json({ service: "auth", status: "ERROR", error: "Database not connected" });
    }
    
    const userCount = await AppDataSource.query("SELECT COUNT(*) as count FROM users");
    res.json({ 
      service: "auth", 
      status: "OK", 
      users: parseInt(userCount[0].count),
      database: "postgresql"
    });
  } catch (error) {
    res.json({ service: "auth", status: "ERROR", error: error.message });
  }
});

app.get("/rest/v1/health", (req: Request, res: Response) => {
  res.json({ 
    service: "database", 
    status: "OK",
    database: AppDataSource.isInitialized ? "connected" : "disconnected"
  });
});

// Basic PostgREST-like endpoint
app.get("/rest/v1/*", (req: Request, res: Response) => {
  const apikey = req.headers.apikey || req.headers.authorization?.replace('Bearer ', '');
  if (!apikey) {
    return res.status(401).json({ error: "API key required", hint: "Provide API key in \"apikey\" header or as Bearer token" });
  }
  
  res.json({ 
    message: "PostgREST endpoint working", 
    path: req.path,
    database: AppDataSource.isInitialized ? "connected" : "disconnected"
  });
});

// Admin routes
app.use("/admin/v1", adminRouter);

const httpServer = createServer(app);

async function createDefaultAdmin() {
  if (!process.env.DEFAULT_ADMIN_EMAIL || !process.env.DEFAULT_ADMIN_PASSWORD) {
    console.log("⚠️  No default admin credentials provided in environment variables");
    return;
  }

  try {
    // Check if admin already exists
    const existingAdmin = await AppDataSource.query(
      "SELECT id FROM users WHERE email = $1", 
      [process.env.DEFAULT_ADMIN_EMAIL]
    );
    
    if (existingAdmin.length > 0) {
      console.log(`✅ Default admin user already exists: ${process.env.DEFAULT_ADMIN_EMAIL}`);
      return;
    }
    
    // Create default workspace first
    let defaultWorkspaceId;
    const existingWorkspace = await AppDataSource.query(
      "SELECT id FROM workspaces WHERE name = $1", 
      ['Default Workspace']
    );
    
    if (existingWorkspace.length > 0) {
      defaultWorkspaceId = existingWorkspace[0].id;
    } else {
      const workspaceResult = await AppDataSource.query(`
        INSERT INTO workspaces (name, description, settings)
        VALUES ($1, $2, $3)
        RETURNING id
      `, [
        'Default Workspace',
        'Default workspace for platform administration',
        '{}'
      ]);
      defaultWorkspaceId = workspaceResult[0].id;
      console.log(`✅ Default workspace created: ${defaultWorkspaceId}`);
    }
    
    // Create default admin
    const passwordHash = await bcrypt.hash(process.env.DEFAULT_ADMIN_PASSWORD, 10);
    
    const result = await AppDataSource.query(`
      INSERT INTO users (
        email, password_hash, role, admin_role, 
        is_platform_admin, is_workspace_admin, can_access_admin,
        workspace_id, permissions, email_verified
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING id, email
    `, [
      process.env.DEFAULT_ADMIN_EMAIL,
      passwordHash,
      'platform_admin',
      'platform_admin',
      true,
      true,
      true,
      defaultWorkspaceId,
      ['*'],
      true
    ]);
    
    // Update workspace owner
    await AppDataSource.query(
      "UPDATE workspaces SET owner_id = $1 WHERE id = $2",
      [result[0].id, defaultWorkspaceId]
    );
    
    console.log(`✅ Default admin user created: ${result[0].email} (ID: ${result[0].id})`);
    console.log(`✅ User assigned to workspace: ${defaultWorkspaceId}`);
  } catch (error) {
    console.error('❌ Failed to create default admin user:', error);
  }
}

AppDataSource.initialize()
  .then(async () => {
    console.log("✅ Database connection initialized successfully!");
    
    // Create default admin user
    await createDefaultAdmin();

    const PORT = process.env.PORT || 4000;
    httpServer.listen(PORT, () => {
      console.log(`🚀 E-BaaS PostgreSQL Backend Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/`);
      console.log(`🔐 Auth endpoint: http://localhost:${PORT}/auth/v1`);
      console.log(`💾 Database endpoint: http://localhost:${PORT}/rest/v1`);
      console.log(`🐘 PostgreSQL Database: ${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_DATABASE}`);
      console.log("✨ PostgreSQL backend server is ready!");
    });
  })
  .catch((error: Error) => {
    console.error("❌ Failed to initialize database:", error);
    
    // Fallback: start server without database for debugging
    const PORT = process.env.PORT || 4000;
    httpServer.listen(PORT, () => {
      console.log(`⚠️  E-BaaS Backend Server running on port ${PORT} (WITHOUT DATABASE)`);
      console.log(`📊 Health check: http://localhost:${PORT}/`);
      console.log("❌ Database connection failed - some features may not work");
    });
  });

export default app;