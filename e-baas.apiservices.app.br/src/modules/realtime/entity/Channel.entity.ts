import "reflect-metadata";
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { ChannelType } from "../dto/realtime.dto";
import { ChannelSubscription } from "./ChannelSubscription.entity";

@Entity("realtime_channels")
@Index(["workspaceId", "name"], { unique: true })
@Index(["workspaceId"])
@Index(["type"])
export class Channel {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100 })
  name: string;

  @Column({ name: "workspace_id", type: "uuid" })
  workspaceId: string;

  @Column({
    type: "enum",
    enum: ChannelType,
    default: ChannelType.BROADCAST
  })
  type: ChannelType;

  @Column({ type: "text", nullable: true })
  description?: string;

  @Column({
    name: "is_private",
    type: "boolean",
    default: false
  })
  isPrivate: boolean;

  @Column({
    name: "max_connections",
    type: "integer",
    nullable: true
  })
  maxConnections?: number;

  @Column({
    type: "json",
    nullable: true
  })
  config?: Record<string, any>;

  @Column({
    name: "allowed_events",
    type: "simple-array",
    nullable: true
  })
  allowedEvents?: string[];

  @Column({
    name: "connection_count",
    type: "integer",
    default: 0
  })
  connectionCount: number;

  @Column({
    name: "message_count",
    type: "bigint",
    default: 0
  })
  messageCount: number;

  @Column({
    name: "last_activity",
    type: "timestamp",
    nullable: true
  })
  lastActivity?: Date;

  @Column({
    name: "is_active",
    type: "boolean",
    default: true
  })
  isActive: boolean;

  @Column({
    name: "created_by",
    type: "uuid",
    nullable: true
  })
  createdBy?: string;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @OneToMany(() => ChannelSubscription, subscription => subscription.channel)
  subscriptions?: ChannelSubscription[];

  @BeforeInsert()
  @BeforeUpdate()
  validateChannel() {
    // Normalize channel name
    this.name = this.name.toLowerCase().replace(/[^a-z0-9-_:]/g, '-');
    
    // Validate channel name format
    if (!/^[a-z0-9][a-z0-9-_:]*[a-z0-9]$/.test(this.name) && this.name.length > 1) {
      throw new Error('Channel name must contain only lowercase letters, numbers, hyphens, underscores, and colons');
    }

    if (this.name.length < 3 || this.name.length > 100) {
      throw new Error('Channel name must be between 3 and 100 characters long');
    }

    // Update last activity
    this.lastActivity = new Date();
  }

  // Check if channel can accept new connections
  canAcceptConnection(): boolean {
    if (!this.isActive) {
      return false;
    }

    if (this.maxConnections && this.connectionCount >= this.maxConnections) {
      return false;
    }

    return true;
  }

  // Check if event is allowed in this channel
  isEventAllowed(event: string): boolean {
    if (!this.allowedEvents || this.allowedEvents.length === 0) {
      return true; // No restrictions
    }

    return this.allowedEvents.includes(event) || this.allowedEvents.includes('*');
  }

  // Get channel topic (used for routing)
  getTopic(): string {
    return `${this.workspaceId}:${this.name}`;
  }

  // Get channel path for WebSocket subscription
  getChannelPath(): string {
    return `realtime/${this.workspaceId}/${this.name}`;
  }

  // Update connection count
  updateConnectionCount(delta: number): void {
    this.connectionCount = Math.max(0, this.connectionCount + delta);
    this.lastActivity = new Date();
  }

  // Update message count
  incrementMessageCount(): void {
    this.messageCount += 1;
    this.lastActivity = new Date();
  }

  // Check if channel is database subscription
  isDatabaseChannel(): boolean {
    return this.type === ChannelType.DATABASE;
  }

  // Check if channel is broadcast
  isBroadcastChannel(): boolean {
    return this.type === ChannelType.BROADCAST;
  }

  // Check if channel is presence
  isPresenceChannel(): boolean {
    return this.type === ChannelType.PRESENCE;
  }

  // Get database subscription config
  getDatabaseConfig(): {
    table?: string;
    schema?: string;
    filter?: string;
    event?: string;
  } {
    if (!this.isDatabaseChannel() || !this.config) {
      return {};
    }

    return {
      table: this.config.table,
      schema: this.config.schema || 'public',
      filter: this.config.filter,
      event: this.config.event || '*'
    };
  }

  // Check if user has access to channel
  hasAccess(userId?: string, role?: string): boolean {
    // Public channels are accessible to everyone
    if (!this.isPrivate) {
      return true;
    }

    // Private channels require authentication
    if (!userId && !role) {
      return false;
    }

    // Service role has access to all channels
    if (role === 'service_role') {
      return true;
    }

    // Check if user is creator
    if (userId && this.createdBy === userId) {
      return true;
    }

    // Additional access control could be implemented here
    // based on workspace permissions, etc.

    return false;
  }

  // Convert to safe object
  toSafeObject() {
    return {
      id: this.id,
      name: this.name,
      workspaceId: this.workspaceId,
      type: this.type,
      description: this.description,
      isPrivate: this.isPrivate,
      maxConnections: this.maxConnections,
      connectionCount: this.connectionCount,
      messageCount: this.messageCount,
      lastActivity: this.lastActivity,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      topic: this.getTopic(),
      channelPath: this.getChannelPath()
    };
  }
}