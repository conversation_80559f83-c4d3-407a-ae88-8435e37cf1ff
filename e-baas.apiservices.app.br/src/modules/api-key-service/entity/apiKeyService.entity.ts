import "reflect-metadata";
import { 
  <PERSON>umn, 
  CreateDateColumn, 
  <PERSON><PERSON><PERSON>, 
  PrimaryGeneratedColumn, 
  UpdateDateColumn,
  Index 
} from "typeorm";

@Entity("api-key-service")
@Index(["workspaceId"])
@Index(["name", "workspaceId"], { unique: true })
export class ApiKeyService {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100 })
  name: string;

  @Column({ name: "workspace_id", type: "uuid" })
  workspaceId: string;

  @Column({ type: "text", nullable: true })
  description?: string;

  @Column({ name: "service_type", type: "varchar", length: 50, default: "api" })
  serviceType: string;

  @Column({ name: "service_config", type: "jsonb", nullable: true })
  serviceConfig?: Record<string, any>;

  @Column({ name: "rate_limit", type: "integer", nullable: true })
  rateLimit?: number;

  @Column({ name: "rate_limit_window", type: "integer", default: 3600 })
  rateLimitWindow: number;

  @Column({ name: "allowed_origins", type: "simple-array", nullable: true })
  allowedOrigins?: string[];

  @Column({ name: "allowed_ips", type: "simple-array", nullable: true })
  allowedIps?: string[];

  @Column({ name: "is_active", type: "boolean", default: true })
  isActive: boolean;

  @Column({ name: "expires_at", type: "timestamp", nullable: true })
  expiresAt?: Date;

  @Column({ name: "last_used_at", type: "timestamp", nullable: true })
  lastUsedAt?: Date;

  @Column({ name: "usage_count", type: "bigint", default: 0 })
  usageCount: number;

  @Column({ name: "created_by", type: "uuid", nullable: true })
  createdBy?: string;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  // Check if service is expired
  get isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  // Check if service can be used
  get canBeUsed(): boolean {
    return this.isActive && !this.isExpired;
  }

  // Update usage statistics
  updateUsage(): void {
    this.lastUsedAt = new Date();
    this.usageCount += 1;
  }

  // Check if origin is allowed
  isOriginAllowed(origin: string): boolean {
    if (!this.allowedOrigins || this.allowedOrigins.length === 0) {
      return true; // No restrictions
    }
    return this.allowedOrigins.includes(origin) || this.allowedOrigins.includes('*');
  }

  // Check if IP is allowed
  isIpAllowed(ip: string): boolean {
    if (!this.allowedIps || this.allowedIps.length === 0) {
      return true; // No restrictions
    }
    return this.allowedIps.includes(ip) || this.allowedIps.includes('*');
  }

  // Convert to safe object (without sensitive data)
  toSafeObject() {
    return {
      id: this.id,
      name: this.name,
      workspaceId: this.workspaceId,
      description: this.description,
      serviceType: this.serviceType,
      rateLimit: this.rateLimit,
      rateLimitWindow: this.rateLimitWindow,
      allowedOrigins: this.allowedOrigins,
      isActive: this.isActive,
      expiresAt: this.expiresAt,
      lastUsedAt: this.lastUsedAt,
      usageCount: this.usageCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      isExpired: this.isExpired,
      canBeUsed: this.canBeUsed
    };
  }
}
