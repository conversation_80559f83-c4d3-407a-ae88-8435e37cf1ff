import "reflect-metadata";
import { Column, <PERSON><PERSON>ty, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from "typeorm";

@Entity("rls-policies")
export class RlsPolicies {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: "uuid" })
  workspaceId: string;

  @Column({ type: "varchar", length: 255 })
  tableName: string;

  @Column({ type: "varchar", length: 255 })
  policyName: string;

  @Column({ type: "varchar", length: 50 })
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'ALL';

  @Column({ type: "text" })
  condition: string;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @Column({ type: "uuid", nullable: true })
  createdBy?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
