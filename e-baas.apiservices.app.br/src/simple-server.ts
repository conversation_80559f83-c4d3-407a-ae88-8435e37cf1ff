import cors from "cors";
import dotenv from "dotenv";
import express, { Express, Request, Response } from "express";

dotenv.config();

const app: Express = express();
app.use(express.json());
app.use(
  cors({
    origin: "*",
  })
);

// In-memory user storage for demo
const users: any[] = [];

// Health check endpoint
app.get("/", (req: Request, res: Response) => {
  res.json({ 
    message: "E-BaaS Backend Server Running", 
    timestamp: new Date().toISOString(),
    status: "OK",
    version: "1.0.0",
    database: "memory"
  });
});

// Auth endpoints
app.post("/auth/v1/signup", (req: Request, res: Response) => {
  try {
    const { email, password, role = "user", adminRole, isPlatformAdmin, canAccessAdmin } = req.body;
    
    // Check if user already exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(400).json({ error: "User already exists" });
    }
    
    // Create new user
    const newUser = {
      id: `user_${Date.now()}`,
      email,
      password, // In real app, this should be hashed
      role,
      adminRole: adminRole || (isPlatformAdmin ? 'platform_admin' : 'user'),
      workspaceId: 'default_workspace',
      permissions: isPlatformAdmin ? ['*'] : ['read'],
      isPlatformAdmin: !!isPlatformAdmin,
      isWorkspaceAdmin: !!canAccessAdmin,
      canAccessAdmin: !!canAccessAdmin || !!isPlatformAdmin,
      createdAt: new Date().toISOString()
    };
    
    users.push(newUser);
    
    // Generate mock JWT token
    const token = `mock_jwt_${Buffer.from(JSON.stringify({ userId: newUser.id, email })).toString('base64')}`;
    
    res.status(201).json({
      access_token: token,
      token_type: "bearer",
      expires_in: 3600,
      user: {
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
        adminRole: newUser.adminRole,
        canAccessAdmin: newUser.canAccessAdmin
      }
    });
  } catch (error) {
    res.status(500).json({ error: "Internal server error" });
  }
});

app.post("/auth/v1/login", (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;
    
    // Find user
    const user = users.find(u => u.email === email && u.password === password);
    if (!user) {
      return res.status(401).json({ error: "Invalid credentials" });
    }
    
    // Generate mock JWT token
    const token = `mock_jwt_${Buffer.from(JSON.stringify({ userId: user.id, email })).toString('base64')}`;
    
    res.json({
      access_token: token,
      token_type: "bearer",
      expires_in: 3600,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        adminRole: user.adminRole,
        workspaceId: user.workspaceId,
        permissions: user.permissions,
        isPlatformAdmin: user.isPlatformAdmin,
        isWorkspaceAdmin: user.isWorkspaceAdmin,
        canAccessAdmin: user.canAccessAdmin
      }
    });
  } catch (error) {
    res.status(500).json({ error: "Internal server error" });
  }
});

app.get("/auth/v1/user", (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: "No token provided" });
    }
    
    const token = authHeader.split(' ')[1];
    if (!token.startsWith('mock_jwt_')) {
      return res.status(401).json({ error: "Invalid token" });
    }
    
    // Decode mock token
    const payload = JSON.parse(Buffer.from(token.replace('mock_jwt_', ''), 'base64').toString());
    const user = users.find(u => u.id === payload.userId);
    
    if (!user) {
      return res.status(401).json({ error: "User not found" });
    }
    
    res.json({
      id: user.id,
      email: user.email,
      role: user.role,
      adminRole: user.adminRole,
      workspaceId: user.workspaceId,
      permissions: user.permissions,
      isPlatformAdmin: user.isPlatformAdmin,
      isWorkspaceAdmin: user.isWorkspaceAdmin,
      canAccessAdmin: user.canAccessAdmin
    });
  } catch (error) {
    res.status(401).json({ error: "Invalid token" });
  }
});

// Health endpoints
app.get("/auth/v1/health", (req: Request, res: Response) => {
  res.json({ service: "auth", status: "OK", users: users.length });
});

app.get("/rest/v1/health", (req: Request, res: Response) => {
  res.json({ service: "database", status: "OK" });
});

// Basic PostgREST-like endpoint
app.get("/rest/v1/*", (req: Request, res: Response) => {
  const apikey = req.headers.apikey || req.headers.authorization?.replace('Bearer ', '');
  if (!apikey) {
    return res.status(401).json({ error: "API key required", hint: "Provide API key in \"apikey\" header or as Bearer token" });
  }
  
  res.json({ message: "PostgREST endpoint working", path: req.path });
});

const PORT = process.env.PORT || 4000;

app.listen(PORT, () => {
  console.log(`🚀 E-BaaS Simple Backend Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/`);
  console.log(`🔐 Auth endpoint: http://localhost:${PORT}/auth/v1`);
  console.log(`💾 Database endpoint: http://localhost:${PORT}/rest/v1`);
  console.log("✨ Simple backend server is ready!");
  
  // Auto-create default admin user if specified in env
  if (process.env.DEFAULT_ADMIN_EMAIL && process.env.DEFAULT_ADMIN_PASSWORD) {
    setTimeout(() => {
      console.log("🔧 Auto-creating default admin user...");
      const adminUser = {
        id: `admin_${Date.now()}`,
        email: process.env.DEFAULT_ADMIN_EMAIL,
        password: process.env.DEFAULT_ADMIN_PASSWORD,
        role: process.env.DEFAULT_ADMIN_ROLE || 'platform_admin',
        adminRole: 'platform_admin',
        workspaceId: 'default_workspace',
        permissions: ['*'],
        isPlatformAdmin: true,
        isWorkspaceAdmin: true,
        canAccessAdmin: true,
        createdAt: new Date().toISOString()
      };
      users.push(adminUser);
      console.log(`✅ Default admin user created: ${adminUser.email}`);
    }, 1000);
  }
});

export default app;