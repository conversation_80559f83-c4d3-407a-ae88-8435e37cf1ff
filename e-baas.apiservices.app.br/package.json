{"name": "e-baas", "version": "1.0.0", "description": "API RESTful que simula as funcionalidades básicas do Supabase de forma simplificada", "main": "index.js", "scripts": {"start:dev": "nodemon --watch src --exec 'ts-node -r tsconfig-paths/register' src/index.ts", "dev": "ts-node -r tsconfig-paths/register src/index.ts", "dev:simple": "ts-node --transpile-only src/simple-server.ts", "dev:postgres": "ts-node --transpile-only src/postgres-server.ts", "dev:server": "NODE_ENV=development ts-node-dev --transpileOnly --ignore-watch node_modules src/index.ts", "build": "tsc --noEmitOnError false --skipLib<PERSON>he<PERSON> || true", "start": "node dist/index.js", "start:ts": "ts-node src/index.ts", "typeorm": "typeorm-ts-node-commonjs", "prepare": "husky", "commit": "npm test && git add . && npx git-cz", "test": "npm run db:reset && npx jest", "test:watch": "npx jest --watch", "gen": "plop", "migrations:run": "npm run typeorm -- migration:run -d src/infra/database/data-source.ts", "migrations:undo": "npm run typeorm -- migration:revert -d src/infra/database/data-source.ts", "test:coverage": "npm run migrations:run && npx jest --coverage", "db:reset": "rm -rf src/infra/database/data.sqlite && npm run migrations:run", "migrations:create": "npm run typeorm -- migration:create src/infra/database/migrations/$npm_config_name", "cdn": "npx tsx src/scripts/cdn-cli.ts", "cdn:info": "npm run cdn info", "cdn:purge": "npm run cdn purge", "cdn:metrics": "npm run cdn metrics", "docker:build": "docker build -t e-baas .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f app", "docker:restart": "docker-compose restart app"}, "keywords": ["supabase", "baas", "api", "database", "authentication"], "author": "", "license": "ISC", "dependencies": {"@faker-js/faker": "^9.0.0", "@types/archiver": "^6.0.3", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/multer": "^1.4.13", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth20": "^2.0.16", "@types/pg": "^8.15.4", "@types/redis": "^4.0.10", "@types/socket.io": "^3.0.1", "@types/ws": "^8.18.1", "archiver": "^7.0.1", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "commander": "^14.0.0", "config": "^3.3.12", "cors": "^2.8.5", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.19.2", "git-cz": "^4.9.0", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "minio": "^8.0.5", "mongodb": "^5.7.0", "multer": "^2.0.1", "mysql2": "^3.6.0", "node-fetch": "^3.3.2", "node-postgres": "^0.6.2", "nodemailer": "^7.0.3", "pagarme": "^4.35.2", "pagarme-js-types": "^2.9.7", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "pg": "^8.12.0", "plop": "^4.0.1", "postgrest-js": "^2.0.0-rc1", "redis": "^4.7.1", "reflect-metadata": "^0.1.13", "sdk-queues": "git+https://****************************************:<EMAIL>/etech-solutions/sdk-queues.apisevices.app.br.git", "sharp": "^0.34.2", "socket.io": "^4.8.1", "sqlite3": "^5.1.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "typeorm": "0.3.20", "uuid": "^10.0.0", "ws": "^8.18.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/config": "^3.3.5", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^16.11.10", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^10.0.0", "eslint": "^9.10.0", "globals": "^15.9.0", "husky": "^9.1.5", "jest": "^29.7.0", "nodemon": "^3.1.4", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "4.5.2", "typescript-eslint": "^8.5.0"}, "husky": {"hooks": {"prepare-commit-msg": "exec < /dev/tty && git cz --hook || true", "prepare-commit-push": "npm run test"}}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}}